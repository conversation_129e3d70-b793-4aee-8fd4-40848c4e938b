package ptype.basic;

import org.junit.platform.suite.api.Suite;
import org.junit.platform.suite.api.SuiteDisplayName;
import org.junit.platform.suite.api.SelectClasses;

/**
 * JUnit Test Suite for all PTType basic tests.
 * 
 * This suite runs all individual test classes in the ptype.basic package:
 * - PTStringTest: Tests for PTString type operations and conversions
 * - PTBooleanTest: Tests for PTBoolean type operations and conversions  
 * - PTIntegerTest: Tests for PTInteger type operations and conversions
 * - PTFloatTest: Tests for PTFloat type operations and conversions
 * - PTDoubleTest: Tests for PTDouble type operations and conversions
 * - PTArrayTest: Tests for PTArray type operations with multiple element types
 * - PTStructTest: Tests for PTStruct type operations with multiple fields
 * 
 * Usage:
 * Run this test suite to execute all basic PTType tests in one go.
 * Individual test classes can still be run separately if needed.
 * 
 * To run this suite:
 * - From IDE: Right-click on this class and select "Run All.java"
 * - From command line: Use your build tool (<PERSON><PERSON>/Gradle) to run this test class
 * 
 * Note: This suite includes the comprehensive AllPTTypeBasicTests class which
 * provides detailed test execution with known bug handling.
 */
@Suite
@SuiteDisplayName("All PTType Basic Tests")
@SelectClasses({
    PTStringTest.class,
    PTBooleanTest.class,
    PTIntegerTest.class,
    PTFloatTest.class,
    PTDoubleTest.class,
    PTArrayTest.class,
    PTStructTest.class
})
public class All {
    // This class serves as a test suite runner.
    // No additional code is needed - the @Suite annotations handle everything.
}
