package javaHelpers;

/**
 * Utility class providing helper methods for number manipulation, bit operations,
 * and byte array conversions. This class contains static methods for working with
 * numeric data at the bit and byte level.
 */
public class NumberHelpers {
    
    /**
     * Calculates the minimum number of bytes needed to store a range of values.
     * @param minValue The minimum value in the range
     * @param maxValue The maximum value in the range
     * @return The number of bytes needed (1-8)
     */
    public static short getNumberBytesNeeded(long minValue,long maxValue ){
        long x = maxValue - minValue;
        short s = 1;
        while (s < 8 && x >= (1L << (s * 8))) s++;
        return s;
    }

    /**
     * Returns the number of bits needed to store the range of values.
     * @param minValue The minimum value in the range
     * @param maxValue The maximum value in the range
     * @return The number of bits needed
     */
    public static byte getNumberBitsNeeded(long minValue,long maxValue){
        long x = maxValue - minValue;
        byte s = 0;
        while(s<Long.SIZE && (x >> s) > 0 ){
            s++;
        }
        return s;
    }

    /**
     * Calculates the maximum value that can be stored with the given number of bytes.
     * @param numBytes The number of bytes available
     * @return The maximum value that can be stored
     */
    public static long getMaxValueWithNumberBytes(short numBytes)
    {
        long result = 0;
        for(int i =0;i < numBytes;++i){
            result = result << Byte.SIZE;
            result += 0xFF;
        }
        return result;
    }

    /**
     * Converts a byte array to a long value (big-endian).
     * @param buf The byte array to convert
     * @return The resulting long value
     */
    public static long byteToLong(byte[] buf) {
        short bufSize = (short) buf.length;
        long result = 0;
        for(short i = 0; i< bufSize; i++){
            result <<= Byte.SIZE;
            result |= (buf[i] & 0xFF);
        }
        return result;
    }

    /**
     * Extracts a specific byte from a long value.
     * @param value The long value to extract from
     * @param byteIdx The byte index (0 = least significant byte)
     * @return The byte at the specified index
     */
    public static byte longToByte(long value,int byteIdx)
    {
        value = value >> byteIdx * Byte.SIZE;
        return (byte)value;
    }

    /**
     * Converts a long value to a byte array (big-endian).
     * @param value The long value to convert
     * @param buf The destination byte array
     * @throws RuntimeException if value is too large for the buffer
     */
    public static void longToBuf(long value, byte[] buf)
    {
        short bufSize = (short) buf.length;
        ASSERT.IFTHROW(NumberHelpers.getMaxValueWithNumberBytes(bufSize)< value,"values is larger than max size");

        for(short i = (short) (bufSize -1); i>=0; i--){
            buf[i] = (byte)(value & 0xFF);
            value >>= Byte.SIZE;
        }
    }

    /**
     * Calculates the number of bytes needed to store the given number of bits.
     * @param numBits The number of bits
     * @return The number of bytes needed (rounded up)
     */
    public static long bitsToBytesNeeded(long numBits)
    {
        long numBytes = numBits / Byte.SIZE;
        if (numBits % Byte.SIZE != 0){
            ++numBytes;
        }
        return numBytes;
    }

    /**
     * Calculates bytes needed considering a starting bit position.
     * @param numBits The number of bits to store
     * @param posInBits The starting bit position
     * @return The number of bytes needed
     */
    public static long bitsToBytesNeededWithPos(long numBits,long posInBits)
    {
        if (numBits == 0){
            return 0;
        }
        byte AmountUsedInFirstByte = 0;
        byte remainderBits = remainderBits(posInBits);
        if (remainderBits >= 0) {
            AmountUsedInFirstByte = (byte) (Byte.SIZE - remainderBits);
        }

        long amountRemaining = numBits - AmountUsedInFirstByte;
        if (amountRemaining < 0){
            return 1;
        }

        return bitsToBytesNeeded(amountRemaining) + 1;
    }

    /**
     * Calculates which byte contains the specified bit.
     * @param numBits The bit position
     * @return The byte index
     */
    public static long byteToGetBit(long numBits){
        long retVal = numBits / Byte.SIZE;
        return retVal;
    }

    /**
     * Calculates the byte position that includes the specified bit.
     * @param numBits The bit position
     * @return The byte position
     */
    public static long bitToIncludedBytePos(long numBits)
    {
        return numBits/Byte.SIZE;
    }

    /**
     * Calculates the remainder bits within a byte.
     * @param numBits The bit position
     * @return The remainder bits (0-7)
     */
    public static byte remainderBits(long numBits)
    {
        return (byte)(numBits % Byte.SIZE);
    }

    /**
     * Calculates how many items are needed to achieve byte alignment.
     * @param numBits The number of bits per item
     * @return The number of items needed for byte alignment
     */
    public static int numItemsNeededToByteAlign(long numBits){
        int numberNeeded = 0;
        long remainingbits = numBits % Byte.SIZE;
        while(remainingbits != 0){
            remainingbits = (numBits * ++numberNeeded) % Byte.SIZE;
        }
        return numberNeeded;
    }

    /**
     * Finds the number of items needed to achieve byte alignment for a desired count.
     * @param numBits The number of bits per item
     * @param numDesired The desired number of items
     * @return The adjusted number of items for byte alignment
     */
    public static long findNumberItemsToByteAlign(long numBits, long numDesired)
    {
        int numToByteAlign = numItemsNeededToByteAlign(numBits);

        if (numToByteAlign == 0){
            return numDesired;
        }

        int itemAdd = 0;
        int remainItems = (int)(numDesired % numToByteAlign);
        while(remainItems != 0){
            remainItems = (int)((numDesired + ++itemAdd) % numToByteAlign);
        }

        return numDesired + itemAdd;
    }

    /**
     * Creates a mask with the least significant bits set.
     * @param numberbits The number of bits to set (from LSB)
     * @return The mask value
     */
    public static long maskLS(byte numberbits)
    {
        /*if (numberbits == Long.SIZE ){
            return 0xffff_ffff_ffff_ffffL;
        }*/
        if (numberbits == 0){
            return 0L;
        }
        long ret = 0xffff_ffff_ffff_ffffL;
        ret = ret >>> Long.SIZE - numberbits;
        return ret;
    }

    /**
     * Creates a mask with the most significant bits set.
     * @param numberbits The number of bits to set (from MSB)
     * @return The mask value
     */
    public static long maskMS(byte numberbits)
    {
        if (numberbits ==0){
            return 0;
        }

        long ret = 0xffff_ffff_ffff_ffffL;
        ret = ret <<  Long.SIZE - numberbits;
        return ret;
    }

    /**
     * Creates a zero mask for the most significant bits.
     * @param numberbits The number of bits to zero (from MSB)
     * @return The mask value
     */
    public static long zeroMaskMS(byte numberbits)
    {
        ASSERT.IFTHROW(numberbits> Long.SIZE,"number bits must be less than Sizeof long");
        if (numberbits == Long.SIZE){
            return 0;
        }

        long ret = 0xffff_ffff_ffff_ffffL;
        ret = ret >>> numberbits;
        return ret;
    }

    /**
     * Creates a zero mask for the least significant bits.
     * @param numbits The number of bits to zero (from LSB)
     * @return The mask value
     */
    public static long zeroMaskLS(byte numbits)
    {
        ASSERT.IFTHROW(numbits> Long.SIZE,"number bits must be less than Sizeof long");
        if (numbits == Long.SIZE){
            return 0;
        }
        long ret = 0xffff_ffff_ffff_ffffL;
        ret = ret <<  numbits;
        return ret;
    }

    /**
     * Creates a combined mask with both LS and MS bits set.
     * @param LS The number of least significant bits to set
     * @param MS The number of most significant bits to set
     * @return The combined mask value
     */
    public static long maskLSMS(byte LS,byte MS)
    {
        long ret = maskLS(LS);
        return ret | maskMS(MS);
    }

    /**
     * Performs a logical right shift (unsigned).
     * @param value The value to shift
     * @param numbits The number of bits to shift
     * @return The shifted value
     */
    public static long shiftLS(long value,byte numbits){
        ASSERT.IFTHROW(numbits> Long.SIZE,"number bits must be less than Sizeof long");
        return value >>> numbits;
    }

    /**
     * Performs a left shift.
     * @param value The value to shift
     * @param numbits The number of bits to shift
     * @return The shifted value
     */
    public static long shiftMS(long value,byte numbits){
        ASSERT.IFTHROW(numbits> Long.SIZE,"number bits must be less than Sizeof long");
        return value << numbits;
    }

    /**
     * Inserts bits into a long value at the specified position.
     * @param value The original value
     * @param pos The bit position to insert at
     * @param insertBits The bits to insert
     * @param numbits The number of bits to insert
     * @return The modified value
     */
    public static long insertBitsIntoLong(long value,byte pos,long insertBits,byte numbits){
        ASSERT.IFTHROW(numbits> Long.SIZE,"number bits must be less than Sizeof long");

        // Mask the insert bits to the specified width
        insertBits &= maskLS(numbits);
        insertBits = shiftMS(insertBits, pos);

        byte topMaskBits = (byte)(Long.SIZE -(numbits+pos));
        if (topMaskBits <0){
            topMaskBits = 0;
        }

        long mask = maskLSMS(pos,topMaskBits);
        value &= mask;

        return value | insertBits;
    }

    /**
     * Extracts bits from a long value at the specified position.
     * @param value The value to extract from
     * @param pos The bit position to start extraction
     * @param numbits The number of bits to extract
     * @return The extracted bits
     */
    public static long extractBitsFromLong(long value,byte pos,byte numbits){
        ASSERT.IFTHROW(numbits> Long.SIZE,"number bits must be less than Sizeof long");

        value = shiftLS(value,pos);
        return value & maskLS(numbits);
    }

    /**
     * Inserts a byte value into a long at the specified byte position.
     * @param curValue The current long value
     * @param pos The byte position (0 = least significant byte)
     * @param value The byte value to insert
     * @return The modified long value
     */
    public static long byteToLong(long curValue, byte pos, byte value) {
        long addValue = value;
        addValue &= 0x00000000000000ff;

        addValue = addValue << (Byte.SIZE * pos);
        curValue |= addValue;
        return curValue;
    }

    /**
     * Calculates how many bits are needed in the last byte for a bit sequence.
     * @param numBits The total number of bits
     * @param posInBits The starting bit position
     * @return The number of bits needed in the last byte
     */
    public static byte bitsNeededInLastByte(long numBits, long posInBits) {
        byte AmountUsedInFirstByte = 0;
        byte remainderBits = remainderBits(posInBits);
        if (remainderBits > 0) {
            AmountUsedInFirstByte = (byte) (Byte.SIZE - remainderBits);
        }

        long amountRemaining = numBits - AmountUsedInFirstByte;
        if (amountRemaining < 0){
            return 0;
        }

        return remainderBits(amountRemaining);
    }

    /**
     * Performs sign extension on a value with the specified bit width.
     * @param curValue The value to sign extend
     * @param maxNumberbits The maximum number of bits in the value
     * @return The sign-extended value
     */
    public static long signExtend(long curValue,byte maxNumberbits){
        ASSERT.IFTHROW(maxNumberbits>Long.SIZE,"max number bits exceeded");
        
        // Check the sign bit
        long topbit = NumberHelpers.extractBitsFromLong(curValue, (byte) (maxNumberbits-1),(byte)1);
        if (topbit == 0){
            // Positive number, no need to sign extend
            return curValue;
        }

        // Negative number, extend the sign bit
        long highMask = zeroMaskLS((byte) (Long.SIZE - maxNumberbits));
        return highMask | curValue;
    }

    public static long getMaxValue(long ...lstValues){
        long retValue = 0;
        for(Long value : lstValues){
            retValue = Math.max(retValue, value);
        }
        return retValue;
    }

    public static long getMinValue(long ...lstValues){
        long retValue = Long.MAX_VALUE;
        for(Long value : lstValues){
            retValue = Math.min(retValue, value);
        }
        return retValue;
    }
}